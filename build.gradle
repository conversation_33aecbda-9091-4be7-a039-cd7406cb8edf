buildscript {
    ext.kotlin_version = "1.6.0"
    repositories {
        repositories {
//            maven {
//                allowInsecureProtocol true
//                url 'https://Jfrog.sgmw.com.cn/artifactory/android-maven-release/'
//                credentials {
//                    username 'maven_reader'
//                    password 'cmVmdGtuOjAxOjE3ODcxMjI0OTQ6b0ZUbkJKRFJ6Rk9hWkc5NkNFelM2S0ZiT1lz'
//                }
//            }

            maven {
                allowInsecureProtocol true
                url   'http://syshome.autoai.com/artifactory/repository/sw-release/'
                credentials {
                    username 'swdeveloper'
                    password 'swdeveloper'
                }
            }
            maven {
                allowInsecureProtocol true
                url   'http://syshome.autoai.com/artifactory/repository/sw-snapshot/'
                credentials {
                    username 'swdeveloper'
                    password 'swdeveloper'
                }
            }
            maven { url 'https://maven.aliyun.com/repository/google/' }
            maven { url 'https://maven.aliyun.com/repository/jcenter/' }
        }
        dependencies {
            classpath 'com.android.tools.build:gradle:7.2.2'
            // NoTE: Do not place your application dependencies her;
            // they belongint the individual module build.gradle file
            classpath 'org.jacoco:org.jacoco.core:0.8.6'
        }
    }
}


allprojects {
    repositories {
//        maven {
//            allowInsecureProtocol true
//            url 'https://Jfrog.sgmw.com.cn/artifactory/android-maven-release/'
//            credentials {
//                username 'maven_reader'
//                password 'cmVmdGtuOjAxOjE3ODcxMjI0OTQ6b0ZUbkJKRFJ6Rk9hWkc5NkNFelM2S0ZiT1lz'
//            }
//        }
        maven {
            allowInsecureProtocol true
            url   'http://syshome.autoai.com/artifactory/repository/sw-release/'
            credentials {
                username 'swdeveloper'
                password 'swdeveloper'
            }
        }
        maven {
            allowInsecureProtocol true
            url   'http://syshome.autoai.com/artifactory/repository/sw-snapshot/'
            credentials {
                username 'swdeveloper'
                password 'swdeveloper'
            }
        }
        maven { url 'https://maven.aliyun.com/repository/google/' }
        maven { url 'https://maven.aliyun.com/repository/jcenter/' }
        google()
        mavenCentral()
    }
}